{% extends "base.html" %}

{% block content %}
<div class="container-fluid mt-4">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-users me-2"></i>
                        姓名映射管理
                    </h5>
                    <button type="button" class="btn btn-primary btn-sm" data-bs-toggle="modal" data-bs-target="#addMappingModal">
                        <i class="fas fa-plus me-1"></i>
                        添加映射
                    </button>
                </div>
                <div class="card-body">
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <p class="text-muted">
                                管理JIRA任务创建时的姓名到OA账号映射关系。当创建任务时，系统会自动将负责人的姓名转换为对应的OA账号。
                            </p>
                        </div>
                        <div class="col-md-6">
                            <div class="input-group">
                                <input type="text" class="form-control" id="searchInput" placeholder="搜索姓名或OA账号...">
                                <button class="btn btn-outline-secondary" type="button" id="refreshBtn">
                                    <i class="fas fa-sync-alt"></i>
                                </button>
                            </div>
                        </div>
                    </div>

                    <div id="loadingIndicator" class="text-center d-none">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">加载中...</span>
                        </div>
                        <p class="mt-2">正在加载映射数据...</p>
                    </div>

                    <div id="mappingTableContainer">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead class="table-light">
                                    <tr>
                                        <th>ID</th>
                                        <th>姓名</th>
                                        <th>OA账号</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody id="mappingTableBody">
                                    <!-- 映射数据将在这里显示 -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 添加映射模态框 -->
<div class="modal fade" id="addMappingModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-plus me-2"></i>
                    添加姓名映射
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="addMappingForm">
                    <div class="mb-3">
                        <label for="nameInput" class="form-label">姓名</label>
                        <input type="text" class="form-control" id="nameInput" required>
                        <div class="form-text">请输入完整的中文姓名</div>
                    </div>
                    <div class="mb-3">
                        <label for="loginInput" class="form-label">OA账号</label>
                        <input type="text" class="form-control" id="loginInput" required>
                        <div class="form-text">请输入对应的OA登录账号</div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" id="confirmAddMapping">
                    <i class="fas fa-save me-1"></i>
                    保存
                </button>
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    let mappingData = [];

    // 加载映射数据
    function loadMappings() {
        $('#loadingIndicator').removeClass('d-none');
        $('#mappingTableContainer').addClass('d-none');

        $.get('/api/v1/jira/name-mapping')
            .done(function(response) {
                if (response.success) {
                    mappingData = response.data;
                    displayMappings(mappingData);
                    showAlert('success', `成功加载 ${response.count} 条映射记录`);
                } else {
                    showAlert('danger', '加载映射数据失败');
                }
            })
            .fail(function(xhr) {
                showAlert('danger', '加载映射数据失败：' + xhr.responseText);
            })
            .always(function() {
                $('#loadingIndicator').addClass('d-none');
                $('#mappingTableContainer').removeClass('d-none');
            });
    }

    // 显示映射数据
    function displayMappings(data) {
        const tbody = $('#mappingTableBody');
        tbody.empty();

        if (data.length === 0) {
            tbody.append(`
                <tr>
                    <td colspan="4" class="text-center text-muted">
                        <i class="fas fa-inbox me-2"></i>
                        暂无映射数据
                    </td>
                </tr>
            `);
            return;
        }

        data.forEach(mapping => {
            const row = `
                <tr>
                    <td>${mapping.id}</td>
                    <td>
                        <strong>${mapping.name}</strong>
                    </td>
                    <td>
                        <code>${mapping.login}</code>
                    </td>
                    <td>
                        <button class="btn btn-sm btn-outline-danger" onclick="deleteMapping(${mapping.id}, '${mapping.name}')">
                            <i class="fas fa-trash"></i>
                        </button>
                    </td>
                </tr>
            `;
            tbody.append(row);
        });
    }

    // 搜索功能
    $('#searchInput').on('input', function() {
        const searchTerm = $(this).val().toLowerCase();
        const filteredData = mappingData.filter(mapping => 
            mapping.name.toLowerCase().includes(searchTerm) || 
            mapping.login.toLowerCase().includes(searchTerm)
        );
        displayMappings(filteredData);
    });

    // 刷新按钮
    $('#refreshBtn').on('click', function() {
        loadMappings();
        $('#searchInput').val('');
    });

    // 添加映射
    $('#confirmAddMapping').on('click', function() {
        const name = $('#nameInput').val().trim();
        const login = $('#loginInput').val().trim();

        if (!name || !login) {
            showAlert('warning', '请填写完整的姓名和OA账号');
            return;
        }

        const $btn = $(this);
        const originalText = $btn.html();
        $btn.html('<i class="fas fa-spinner fa-spin me-1"></i>保存中...');
        $btn.prop('disabled', true);

        $.ajax({
            url: '/api/v1/jira/name-mapping',
            type: 'POST',
            contentType: 'application/json',
            data: JSON.stringify({
                name: name,
                login: login
            }),
            success: function(response) {
                if (response.success) {
                    $('#addMappingModal').modal('hide');
                    $('#addMappingForm')[0].reset();
                    loadMappings();
                    showAlert('success', response.message);
                } else {
                    showAlert('danger', '添加映射失败：' + response.message);
                }
            },
            error: function(xhr) {
                let errorMessage = '添加映射失败';
                if (xhr.responseJSON && xhr.responseJSON.detail) {
                    errorMessage = xhr.responseJSON.detail;
                }
                showAlert('danger', errorMessage);
            },
            complete: function() {
                $btn.html(originalText);
                $btn.prop('disabled', false);
            }
        });
    });

    // 删除映射
    window.deleteMapping = function(id, name) {
        if (confirm(`确定要删除姓名 "${name}" 的映射吗？`)) {
            // TODO: 实现删除功能
            showAlert('info', '删除功能开发中...');
        }
    };

    // 显示提示信息
    function showAlert(type, message) {
        const alertHtml = `
            <div class="alert alert-${type} alert-dismissible fade show" role="alert">
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;
        
        $('.alert').remove();
        $('.container-fluid').prepend(alertHtml);
        
        setTimeout(function() {
            $('.alert').fadeOut();
        }, 3000);
    }

    // 页面加载时执行
    loadMappings();
});
</script>
{% endblock %}
