{% extends "base.html" %}

{% block content %}
<div class="container-fluid mt-4">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-users me-2"></i>
                        姓名映射管理
                    </h5>
                    <button type="button" class="btn btn-primary btn-sm" data-bs-toggle="modal" data-bs-target="#addMappingModal">
                        <i class="fas fa-plus me-1"></i>
                        添加映射
                    </button>
                </div>
                <div class="card-body">
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <p class="text-muted">
                                管理JIRA任务创建时的姓名到OA账号映射关系。当创建任务时，系统会自动将负责人的姓名转换为对应的OA账号。
                            </p>
                        </div>
                        <div class="col-md-6">
                            <div class="input-group">
                                <input type="text" class="form-control" id="searchInput" placeholder="搜索姓名或OA账号...">
                                <button class="btn btn-outline-secondary" type="button" id="refreshBtn">
                                    <i class="fas fa-sync-alt"></i>
                                </button>
                            </div>
                        </div>
                    </div>

                    <div id="loadingIndicator" class="text-center d-none">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">加载中...</span>
                        </div>
                        <p class="mt-2">正在加载映射数据...</p>
                    </div>

                    <div id="mappingTableContainer">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead class="table-light">
                                    <tr>
                                        <th>ID</th>
                                        <th>姓名</th>
                                        <th>OA账号</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody id="mappingTableBody">
                                    <!-- 映射数据将在这里显示 -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 添加映射模态框 -->
<div class="modal fade" id="addMappingModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-plus me-2"></i>
                    添加姓名映射
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="addMappingForm">
                    <div class="mb-3">
                        <label for="nameInput" class="form-label">姓名</label>
                        <input type="text" class="form-control" id="nameInput" required>
                        <div class="form-text">请输入完整的中文姓名</div>
                    </div>
                    <div class="mb-3">
                        <label for="loginInput" class="form-label">OA账号</label>
                        <input type="text" class="form-control" id="loginInput" required>
                        <div class="form-text">请输入对应的OA登录账号</div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" id="confirmAddMapping">
                    <i class="fas fa-save me-1"></i>
                    保存
                </button>
            </div>
        </div>
    </div>
</div>

<script>
// 使用原生JavaScript而不是jQuery
document.addEventListener('DOMContentLoaded', function() {
    console.log('文档加载完成');

    // 使用原生fetch API调用
    fetch('/api/v1/jira/name-mapping')
        .then(function(response) {
            return response.json();
        })
        .then(function(data) {
            console.log('API调用成功:', data);
            if (data.success && data.data) {
                var tbody = document.getElementById('mappingTableBody');
                tbody.innerHTML = '';

                data.data.forEach(function(mapping) {
                    var row = document.createElement('tr');
                    row.innerHTML =
                        '<td>' + mapping.id + '</td>' +
                        '<td><strong>' + mapping.name + '</strong></td>' +
                        '<td><code>' + mapping.login + '</code></td>' +
                        '<td><button class="btn btn-sm btn-outline-danger">删除</button></td>';
                    tbody.appendChild(row);
                });

                // 显示成功提示
                var alertDiv = document.createElement('div');
                alertDiv.className = 'alert alert-success alert-dismissible fade show';
                alertDiv.innerHTML = '成功加载 ' + data.count + ' 条映射记录' +
                    '<button type="button" class="btn-close" data-bs-dismiss="alert"></button>';
                document.querySelector('.container-fluid').insertBefore(alertDiv, document.querySelector('.container-fluid').firstChild);
            }
        })
        .catch(function(error) {
            console.error('API调用失败:', error);
            var alertDiv = document.createElement('div');
            alertDiv.className = 'alert alert-danger alert-dismissible fade show';
            alertDiv.innerHTML = '加载映射数据失败：' + error.message +
                '<button type="button" class="btn-close" data-bs-dismiss="alert"></button>';
            document.querySelector('.container-fluid').insertBefore(alertDiv, document.querySelector('.container-fluid').firstChild);
        });
});
</script>
{% endblock %}
