{% extends "base.html" %}

{% block content %}
<div class="container-fluid mt-4">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-database me-2"></i>
                        数据库连接管理
                    </h5>
                    <button type="button" class="btn btn-primary btn-sm" id="testConnection">
                        <i class="fas fa-plug me-1"></i>
                        测试连接
                    </button>
                </div>
                <div class="card-body">
                    <!-- 连接状态 -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="card border-info">
                                <div class="card-body text-center">
                                    <h6 class="card-title">连接状态</h6>
                                    <div id="connectionStatus">
                                        <span class="badge bg-secondary">未测试</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card border-info">
                                <div class="card-body text-center">
                                    <h6 class="card-title">数据库信息</h6>
                                    <div>
                                        <small class="text-muted">主机: 10.51.137.19:15020</small><br>
                                        <small class="text-muted">数据库: srms</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 数据库表列表 -->
                    <div class="row">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header d-flex justify-content-between align-items-center">
                                    <h6 class="mb-0">数据库表列表</h6>
                                    <button type="button" class="btn btn-outline-primary btn-sm" id="loadTables">
                                        <i class="fas fa-sync-alt me-1"></i>
                                        刷新表列表
                                    </button>
                                </div>
                                <div class="card-body">
                                    <div id="tablesLoading" class="text-center d-none">
                                        <div class="spinner-border text-primary" role="status">
                                            <span class="visually-hidden">加载中...</span>
                                        </div>
                                        <p class="mt-2">正在加载表列表...</p>
                                    </div>
                                    
                                    <div id="tablesContainer">
                                        <p class="text-muted">点击"刷新表列表"按钮加载数据库表信息</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 表结构模态框 -->
<div class="modal fade" id="tableStructureModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-table me-2"></i>
                    表结构信息
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="tableStructureContent">
                    <!-- 表结构内容将在这里动态加载 -->
                </div>
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    // 测试数据库连接
    $('#testConnection').on('click', function() {
        const $btn = $(this);
        const originalText = $btn.html();
        
        $btn.html('<i class="fas fa-spinner fa-spin me-1"></i>测试中...');
        $btn.prop('disabled', true);
        
        $.get('/api/v1/database/test')
            .done(function(response) {
                if (response.success) {
                    $('#connectionStatus').html('<span class="badge bg-success">连接成功</span>');
                    showAlert('success', '数据库连接测试成功！');
                } else {
                    $('#connectionStatus').html('<span class="badge bg-danger">连接失败</span>');
                    showAlert('danger', '数据库连接测试失败：' + response.message);
                }
            })
            .fail(function(xhr) {
                $('#connectionStatus').html('<span class="badge bg-danger">连接失败</span>');
                showAlert('danger', '数据库连接测试失败：' + xhr.responseText);
            })
            .always(function() {
                $btn.html(originalText);
                $btn.prop('disabled', false);
            });
    });
    
    // 加载数据库表列表
    $('#loadTables').on('click', function() {
        const $btn = $(this);
        const originalText = $btn.html();
        
        $btn.html('<i class="fas fa-spinner fa-spin me-1"></i>加载中...');
        $btn.prop('disabled', true);
        $('#tablesLoading').removeClass('d-none');
        $('#tablesContainer').empty();
        
        $.get('/api/v1/database/tables')
            .done(function(response) {
                if (response.success && response.data) {
                    displayTables(response.data);
                    showAlert('success', `成功加载 ${response.count} 个数据库表`);
                } else {
                    $('#tablesContainer').html('<p class="text-danger">加载表列表失败</p>');
                    showAlert('danger', '加载表列表失败');
                }
            })
            .fail(function(xhr) {
                $('#tablesContainer').html('<p class="text-danger">加载表列表失败：' + xhr.responseText + '</p>');
                showAlert('danger', '加载表列表失败：' + xhr.responseText);
            })
            .always(function() {
                $btn.html(originalText);
                $btn.prop('disabled', false);
                $('#tablesLoading').addClass('d-none');
            });
    });
    
    // 显示表列表
    function displayTables(tables) {
        let html = '<div class="table-responsive"><table class="table table-hover">';
        html += '<thead><tr><th>表名</th><th>注释</th><th>行数</th><th>操作</th></tr></thead><tbody>';
        
        tables.forEach(function(table) {
            html += `<tr>
                <td><code>${table.table_name}</code></td>
                <td>${table.table_comment || '<span class="text-muted">无注释</span>'}</td>
                <td>${table.table_rows !== null ? table.table_rows.toLocaleString() : 'N/A'}</td>
                <td>
                    <button class="btn btn-outline-info btn-sm view-structure" data-table="${table.table_name}">
                        <i class="fas fa-eye me-1"></i>查看结构
                    </button>
                </td>
            </tr>`;
        });
        
        html += '</tbody></table></div>';
        $('#tablesContainer').html(html);
        
        // 绑定查看结构按钮事件
        $('.view-structure').on('click', function() {
            const tableName = $(this).data('table');
            viewTableStructure(tableName);
        });
    }
    
    // 查看表结构
    function viewTableStructure(tableName) {
        $('#tableStructureModal .modal-title').html(`<i class="fas fa-table me-2"></i>表结构信息 - ${tableName}`);
        $('#tableStructureContent').html('<div class="text-center"><div class="spinner-border text-primary" role="status"></div><p class="mt-2">正在加载表结构...</p></div>');
        $('#tableStructureModal').modal('show');
        
        $.get(`/api/v1/database/table/${tableName}`)
            .done(function(response) {
                if (response.success && response.columns) {
                    displayTableStructure(response.columns);
                } else {
                    $('#tableStructureContent').html('<p class="text-danger">加载表结构失败</p>');
                }
            })
            .fail(function(xhr) {
                $('#tableStructureContent').html('<p class="text-danger">加载表结构失败：' + xhr.responseText + '</p>');
            });
    }
    
    // 显示表结构
    function displayTableStructure(columns) {
        let html = '<div class="table-responsive"><table class="table table-sm">';
        html += '<thead><tr><th>字段名</th><th>数据类型</th><th>允许空值</th><th>默认值</th><th>注释</th></tr></thead><tbody>';
        
        columns.forEach(function(column) {
            html += `<tr>
                <td><code>${column.column_name}</code></td>
                <td><span class="badge bg-secondary">${column.data_type}</span></td>
                <td>${column.is_nullable === 'YES' ? '<span class="text-success">是</span>' : '<span class="text-danger">否</span>'}</td>
                <td>${column.column_default || '<span class="text-muted">无</span>'}</td>
                <td>${column.column_comment || '<span class="text-muted">无注释</span>'}</td>
            </tr>`;
        });
        
        html += '</tbody></table></div>';
        $('#tableStructureContent').html(html);
    }
    
    // 显示提示信息
    function showAlert(type, message) {
        const alertHtml = `
            <div class="alert alert-${type} alert-dismissible fade show" role="alert">
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;
        
        // 移除现有的alert
        $('.alert').remove();
        
        // 添加新的alert到页面顶部
        $('.container-fluid').prepend(alertHtml);
        
        // 3秒后自动消失
        setTimeout(function() {
            $('.alert').fadeOut();
        }, 3000);
    }
});
</script>
{% endblock %}
