#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文件名: web.py
描述: Web页面路由

更新日志:
2024-01-15 20:32 - 创建了Web页面路由模块
"""

from fastapi import APIRouter, Request, Form, UploadFile, File
from fastapi.templating import Jinja2Templates
from fastapi.responses import HTMLResponse, FileResponse
from typing import Optional
import pandas as pd
import os

# 创建路由实例
router = APIRouter()

# 配置模板引擎
templates = Jinja2Templates(directory="app/templates")

@router.get("/", response_class=HTMLResponse)
async def index(request: Request):
    """主页 - 文件上传和配置页面"""
    return templates.TemplateResponse(
        "index.html", 
        {"request": request, "title": "Jira任务批量创建工具"}
    )

@router.get("/preview", response_class=HTMLResponse)
async def preview(request: Request):
    """预览页面 - 显示解析后的任务列表"""
    return templates.TemplateResponse(
        "preview.html", 
        {"request": request, "title": "任务预览"}
    )

@router.get("/result", response_class=HTMLResponse)
async def result(request: Request):
    """结果页面 - 显示任务创建结果"""
    return templates.TemplateResponse(
        "result.html", 
        {"request": request, "title": "创建结果"}
    )

@router.get("/template")
async def download_template():
    """下载Excel模板文件"""
    try:
        # 创建模板数据（移除迭代列，改为用户输入）- 模拟合并单元格结构
        template_data = {
            '橙卡': ['JGKEZH-23876', '', '', 'JGKEZH-23877', '', 'JGKEZH-23878'],
            '模块': ['用户管理', '用户管理', '', '订单处理', '', '支付系统'],
            '主任务': ['实现用户登录功能', '用户权限管理', '用户资料管理', '开发订单管理系统', '订单状态跟踪', '集成支付接口'],
            '子任务': ['设计登录页面UI', '实现权限验证API', '设计资料编辑页面', '实现订单列表API', '开发状态更新功能', '对接支付宝接口'],
            '任务类型': ['UI', 'API', 'UI', 'API', 'API', 'API'],
            '负责人': ['张三', '李四', '张三', '王五', '李四', '王五'],
            '工作量': ['8', '16', '12', '20', '8', '24']
        }

        # 创建DataFrame
        df = pd.DataFrame(template_data)

        # 确保templates目录存在
        template_dir = "app/templates"
        if not os.path.exists(template_dir):
            os.makedirs(template_dir)

        # 生成Excel文件
        template_file = "app/templates/jira_tasks_template.xlsx"
        df.to_excel(template_file, index=False, sheet_name='任务列表')

        # 返回文件下载响应
        return FileResponse(
            path=template_file,
            filename="Jira任务批量创建模板.xlsx",
            media_type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
        )

    except Exception as e:
        return {"error": f"模板生成失败: {str(e)}"}

@router.get("/help", response_class=HTMLResponse)
async def help_page(request: Request):
    """帮助页面"""
    return templates.TemplateResponse(
        "help.html", 
        {"request": request, "title": "使用帮助"}
    )