#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文件名: api.py
描述: API路由

更新日志:
2024-01-15 20:33 - 创建了API路由模块
"""

from fastapi import APIRouter, UploadFile, File, HTTPException, Form
from fastapi.responses import JSONResponse
from typing import List, Dict, Any, Optional
import pandas as pd
import json
import logging
from datetime import datetime
import io

# 创建路由实例
router = APIRouter()

@router.post("/v1/parse-excel")
async def parse_excel(
    file: UploadFile = File(...),
    sheet_name: Optional[str] = Form(None)
):
    """
    解析Excel文件，返回任务列表供预览
    
    参数:
    - file: Excel文件
    - sheet_name: 工作表名称（可选）
    
    返回:
    - 解析后的任务列表
    """
    try:
        # 验证文件类型
        if not file.filename.endswith(('.xlsx', '.xls')):
            raise HTTPException(
                status_code=400, 
                detail="文件格式不支持，请上传Excel文件（.xlsx或.xls）"
            )
        
        # 读取Excel文件
        content = await file.read()

        # 解析Excel内容
        try:
            # 使用BytesIO包装字节内容
            excel_buffer = io.BytesIO(content)
            if sheet_name:
                df = pd.read_excel(excel_buffer, sheet_name=sheet_name)
            else:
                df = pd.read_excel(excel_buffer)
        except Exception as e:
            raise HTTPException(
                status_code=400,
                detail=f"Excel文件解析失败: {str(e)}"
            )
        
        # 打印实际的列名用于调试
        actual_columns = list(df.columns)
        logging.info(f"Excel文件实际列名: {actual_columns}")

        # 验证必要的列是否存在 - 使用更灵活的匹配
        required_columns = ['橙卡', '模块', '主任务', '子任务', '任务类型', '负责人', '工作量', '迭代']

        # 创建列名映射，支持去除空格和不同的列名变体
        column_mapping = {}
        for req_col in required_columns:
            found = False
            for actual_col in actual_columns:
                # 去除空格并比较
                if str(actual_col).strip() == req_col:
                    column_mapping[req_col] = actual_col
                    found = True
                    break
            if not found:
                # 尝试一些常见的变体
                if req_col == '橙卡':
                    for actual_col in actual_columns:
                        if '橙卡' in str(actual_col) or 'demand' in str(actual_col).lower():
                            column_mapping[req_col] = actual_col
                            found = True
                            break
                elif req_col == '任务类型':
                    for actual_col in actual_columns:
                        if '任务类型' in str(actual_col) or '类型' in str(actual_col):
                            column_mapping[req_col] = actual_col
                            found = True
                            break
                # 可以添加更多变体匹配

        missing_columns = [col for col in required_columns if col not in column_mapping]

        if missing_columns:
            raise HTTPException(
                status_code=400,
                detail=f"Excel文件缺少必要的列: {', '.join(missing_columns)}。实际列名: {', '.join(actual_columns)}"
            )

        logging.info(f"列名映射: {column_mapping}")
        
        # 转换数据格式 - 使用英文字段名作为API标准格式
        tasks = []
        for index, row in df.iterrows():
            task = {
                "row": index + 2,  # Excel行号从2开始（第1行是标题）
                "demand": str(row[column_mapping['橙卡']]) if pd.notna(row[column_mapping['橙卡']]) else "",
                "module": str(row[column_mapping['模块']]) if pd.notna(row[column_mapping['模块']]) else "",
                "main_task": str(row[column_mapping['主任务']]) if pd.notna(row[column_mapping['主任务']]) else "",
                "sub_task": str(row[column_mapping['子任务']]) if pd.notna(row[column_mapping['子任务']]) else "",
                "task_type": str(row[column_mapping['任务类型']]) if pd.notna(row[column_mapping['任务类型']]) else "",
                "assignee": str(row[column_mapping['负责人']]) if pd.notna(row[column_mapping['负责人']]) else "",
                "workload": str(row[column_mapping['工作量']]) if pd.notna(row[column_mapping['工作量']]) else "",
                "sprint": str(row[column_mapping['迭代']]) if pd.notna(row[column_mapping['迭代']]) else ""
            }
            tasks.append(task)
        
        return {
            "success": True,
            "message": f"成功解析{len(tasks)}个任务",
            "data": {
                "filename": file.filename,
                "total_count": len(tasks),
                "tasks": tasks,
                "parsed_at": datetime.now().isoformat()
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"服务器内部错误: {str(e)}"
        )

@router.post("/v1/create-issues")
async def create_issues(
    tasks: List[Dict[str, Any]],
    jira_config: Dict[str, str]
):
    """
    创建Jira任务
    
    参数:
    - tasks: 任务列表
    - jira_config: Jira配置信息
    
    返回:
    - 创建结果
    """
    try:
        # 验证Jira配置
        required_config = ['server', 'username', 'password', 'project_key']
        missing_config = [key for key in required_config if key not in jira_config]
        
        if missing_config:
            raise HTTPException(
                status_code=400,
                detail=f"Jira配置缺少必要参数: {', '.join(missing_config)}"
            )
        
        # TODO: 实现Jira任务创建逻辑
        # 这里先返回模拟结果
        results = []
        for task in tasks:
            result = {
                "row_number": task.get("row_number"),
                "main_task": task.get("main_task"),
                "sub_task": task.get("sub_task"),
                "status": "创建成功",
                "jira_key": f"TEST-{task.get('row_number', 0)}",
                "message": "模拟创建成功"
            }
            results.append(result)
        
        return {
            "success": True,
            "message": f"成功创建{len(results)}个任务",
            "data": {
                "total_count": len(results),
                "success_count": len(results),
                "failed_count": 0,
                "results": results,
                "created_at": datetime.now().isoformat()
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"服务器内部错误: {str(e)}"
        )

@router.get("/v1/task-types")
async def get_task_types():
    """获取任务类型列表"""
    task_types = [
        {"id": "11007", "name": "迭代任务", "description": "主任务类型"},
        {"id": "11012", "name": "前端开发子任务", "description": "UI相关的子任务"},
        {"id": "11013", "name": "后端开发子任务", "description": "API相关的子任务"},
        {"id": "11014", "name": "数据开发子任务", "description": "测试相关的子任务"}
    ]
    
    return {
        "success": True,
        "data": task_types
    }

@router.get("/v1/users")
async def get_users():
    """获取用户列表"""
    # TODO: 从数据库获取用户信息
    users = [
        {"oa_account": "zhangsan", "chinese_name": "张三", "email": "<EMAIL>"},
        {"oa_account": "lisi", "chinese_name": "李四", "email": "<EMAIL>"},
        {"oa_account": "wangwu", "chinese_name": "王五", "email": "<EMAIL>"}
    ]
    
    return {
        "success": True,
        "data": users
    }

@router.post("/v1/test-jira-connection")
async def test_jira_connection(jira_config: Dict[str, str]):
    """测试Jira连接"""
    try:
        # 验证必要的配置参数
        required_fields = ['url', 'username', 'token', 'project_key']
        for field in required_fields:
            if field not in jira_config or not jira_config[field]:
                raise HTTPException(
                    status_code=400,
                    detail=f"缺少必要的配置参数: {field}"
                )

        # TODO: 实际的Jira连接测试逻辑
        # 这里应该使用atlassian-python-api库来测试连接
        # from atlassian import Jira
        # jira = Jira(
        #     url=jira_config['url'],
        #     username=jira_config['username'],
        #     password=jira_config['token']
        # )
        # project = jira.project(jira_config['project_key'])

        # 暂时返回成功，实际实现时需要真正测试连接
        return {
            "success": True,
            "message": "连接测试成功"
        }

    except HTTPException:
        raise
    except Exception as e:
        return {
            "success": False,
            "message": f"连接测试失败: {str(e)}"
        }

@router.post("/v1/create-jira-tasks")
async def create_jira_tasks(request_data: Dict[str, Any]):
    """批量创建Jira任务"""
    try:
        tasks = request_data.get('tasks', [])
        jira_config = request_data.get('jira_config', {})

        if not tasks:
            raise HTTPException(
                status_code=400,
                detail="没有要创建的任务"
            )

        # 验证Jira配置
        required_fields = ['url', 'username', 'token', 'project_key', 'issue_type']
        for field in required_fields:
            if field not in jira_config or not jira_config[field]:
                raise HTTPException(
                    status_code=400,
                    detail=f"缺少必要的Jira配置: {field}"
                )

        # TODO: 实际的Jira任务创建逻辑
        # 这里应该使用atlassian-python-api库来创建任务

        # 模拟创建结果
        success_tasks = []
        failed_tasks = []

        for i, task in enumerate(tasks):
            try:
                # 模拟创建任务
                task_key = f"{jira_config['project_key']}-{1000 + i}"
                success_tasks.append({
                    "title": task.get('sub_task', task.get('main_task', '')),
                    "task_type": task.get('task_type', ''),
                    "assignee": task.get('assignee', ''),
                    "key": task_key,
                    "url": f"{jira_config['url']}/browse/{task_key}"
                })
            except Exception as e:
                failed_tasks.append({
                    "title": task.get('sub_task', task.get('main_task', '')),
                    "task_type": task.get('task_type', ''),
                    "assignee": task.get('assignee', ''),
                    "error": str(e)
                })

        return {
            "success": True,
            "data": {
                "success": success_tasks,
                "failed": failed_tasks,
                "summary": {
                    "total": len(tasks),
                    "success_count": len(success_tasks),
                    "failed_count": len(failed_tasks)
                }
            }
        }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"创建任务失败: {str(e)}"
        )

@router.post("/v1/export-results")
async def export_results(request_data: Dict[str, Any]):
    """导出创建结果"""
    try:
        results = request_data.get('results', {})
        include_success = request_data.get('include_success', True)
        include_failed = request_data.get('include_failed', True)
        include_summary = request_data.get('include_summary', True)

        # TODO: 实际的Excel导出逻辑
        # 这里应该使用pandas和openpyxl来生成Excel文件

        # 模拟导出结果
        filename = f"jira_tasks_result_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
        download_url = f"/api/v1/download/{filename}"

        return {
            "success": True,
            "filename": filename,
            "download_url": download_url,
            "message": "导出成功"
        }

    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"导出失败: {str(e)}"
        )